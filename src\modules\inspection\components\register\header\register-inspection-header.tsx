"use client";
import { TInspectionTabValue } from "@/modules/inspection/hooks/tabs/inspection-tabs.hook";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { Input } from "@/shared/components/shadcn/input";
import { TabsList, TabsTrigger } from "@/shared/components/shadcn/tabs";
import { LucideIcon, Plus, Search } from "lucide-react";
import React from "react";

interface RegisterInspectionHeaderProps {
	tabItems: ITabItemRegisterInspectionTabs[];
	activeTab: TInspectionTabValue;
	setActiveTab: (tab: TInspectionTabValue) => void;
	searchTerm: string;
	setSearchTerm: (term: string) => void;
	onNew: (searchTerm: string) => void;
}

export interface ITabItemRegisterInspectionTabs {
	value: TInspectionTabValue;
	label: string;
	icon: LucideIcon;
	renderContent: (searchTerm: string) => React.ReactNode;
	onNew: (searchTerm: string) => void;
	disabled?: boolean;
}

export const RegisterInspectionHeader: React.FC<RegisterInspectionHeaderProps> = ({ tabItems, activeTab, setActiveTab, searchTerm, setSearchTerm, onNew }) => {
	return (
		<div className="mb-4 flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
			<TabsList className="flex w-full flex-wrap gap-2 rounded-none bg-transparent p-0 md:w-auto">
				{tabItems.map(({ value, label, icon: Icon, disabled }) => (
					<TabsTrigger
						key={value}
						value={value}
						disabled={disabled}
						className={`rounded-controls data-[state=active]:bg-primary bg-secondary text-foreground flex items-center gap-2 px-3 py-2 text-sm transition-colors data-[state=active]:text-white data-[state=active]:shadow md:text-base ${
							disabled
								? "cursor-not-allowed border border-dashed border-gray-300 bg-gray-100 text-gray-400 opacity-60 hover:bg-gray-100"
								: "hover:bg-primary/10 hover:text-primary"
						} `}
						tabIndex={disabled ? -1 : 0}
						aria-disabled={disabled}
						title={disabled ? "Funcionalidade em breve" : label}
						onClick={() => !disabled && setActiveTab(value)}
						data-state={activeTab === value ? "active" : undefined}
					>
						<Icon className="mr-2 size-4" />
						{label}
					</TabsTrigger>
				))}
			</TabsList>
			<div className="mt-2 flex w-full flex-col items-stretch gap-2 sm:flex-row md:mt-0 md:w-auto md:items-center md:gap-3">
				<div className="relative w-full sm:w-64">
					<Search className="text-muted-foreground absolute top-1/2 left-3 size-4 -translate-y-1/2 transform" />
					<Input placeholder="Pesquisar..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="w-full pl-10" />
				</div>
				<Button onClick={() => onNew(searchTerm)} className="bg-primary hover:bg-primary/80 flex w-full items-center gap-2 text-white sm:w-auto">
					<Plus className="size-4" />
					Novo
				</Button>
			</div>
		</div>
	);
};
